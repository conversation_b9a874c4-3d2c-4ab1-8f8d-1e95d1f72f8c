package com.kaolafm.kradio.flavor.impl;

import android.util.Log;

import com.kaolafm.kradio.lib.base.flavor.KRadioAudioFocusListenerInter;
import com.kaolafm.opensdk.player.logic.PlayerManager;

import static android.media.AudioManager.AUDIOFOCUS_GAIN;
import static android.media.AudioManager.AUDIOFOCUS_LOSS;
import static android.media.AudioManager.AUDIOFOCUS_LOSS_TRANSIENT;

/**
 * 东风猛士专用音频焦点监听器实现
 * 针对多屏切换场景进行优化，避免不必要的播放中断
 */
public class KRadioAudioFocusListenerImpl implements KRadioAudioFocusListenerInter {
    private static final String TAG = "MengshiAudioFocusListener";

    @Override
    public void onFocusChanged(int status) {
        Log.d(TAG, "onFocusChanged: " + status);
        
        switch (status) {
            case AUDIOFOCUS_LOSS:
            case AUDIOFOCUS_LOSS_TRANSIENT:
                handleAudioFocusLoss(status);
                break;
            case AUDIOFOCUS_GAIN:
                handleAudioFocusGain();
                break;
            default:
                Log.d(TAG, "onFocusChanged: unhandled status " + status);
                break;
        }
    }

    /**
     * 处理音频焦点丢失
     * 在多屏切换场景下，不要因为焦点丢失而暂停播放，避免误暂停
     */
    private void handleAudioFocusLoss(int status) {
        PlayerManager playerManager = PlayerManager.getInstance();

        Log.d(TAG, "handleAudioFocusLoss: status=" + status +
                   ", isPlaying=" + playerManager.isPlaying() +
                   ", isPauseFromUser=" + playerManager.isPauseFromUser());

        // 在多屏环境下，音频焦点丢失通常是Activity切换导致的，不应该暂停播放
        // 只记录日志，不执行暂停操作，让播放状态保持不变
        Log.d(TAG, "handleAudioFocusLoss: 多屏环境下忽略音频焦点丢失，保持播放状态不变");

        // 注释掉原有的暂停逻辑，避免多屏切换时的误暂停
        // if (playerManager.isPlaying()) {
        //     Log.d(TAG, "handleAudioFocusLoss: pausing playback due to focus loss");
        //     playerManager.pause(false);
        // }
    }

    /**
     * 处理音频焦点获得
     * 在多屏切换场景下，如果之前不是用户主动暂停，则自动恢复播放
     */
    private void handleAudioFocusGain() {
        PlayerManager playerManager = PlayerManager.getInstance();
        
        Log.d(TAG, "handleAudioFocusGain: isPlaying=" + playerManager.isPlaying() + 
                   ", isPauseFromUser=" + playerManager.isPauseFromUser());
        
        // 只有在非用户暂停且当前未播放的情况下才自动恢复
        if (!playerManager.isPlaying() && !playerManager.isPauseFromUser()) {
            try {
                // 检查是否有有效的播放项
                if (playerManager.getCurPlayItem() != null) {
                    Log.d(TAG, "handleAudioFocusGain: resuming playback");
                    playerManager.play();
                } else {
                    Log.d(TAG, "handleAudioFocusGain: no valid play item to resume");
                }
            } catch (Exception e) {
                Log.e(TAG, "handleAudioFocusGain: failed to resume playback", e);
            }
        } else {
            Log.d(TAG, "handleAudioFocusGain: not resuming - already playing or user paused");
        }
    }
}
